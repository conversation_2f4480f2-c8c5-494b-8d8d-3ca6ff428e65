from src.utils.logging import logger
from src.utils.migrations import (
    create_registration_automation_config,
    delete_old_launches,
    set_player_allowed_games_from_app_id,
)


async def run_migrations():
    name = "tasks.run_migrations"
    logger.debug(name, "Running migrations...")
    await create_registration_automation_config()
    await delete_old_launches()
    await set_player_allowed_games_from_app_id()
    logger.debug(name, "Migrations completed successfully.")
