from unittest.mock import AsyncMock, patch

import pytest

from src.db.models import FutureTournamentCheck, MultiflightConfiguration, Player
from src.schemas.responses import BotResponse
from src.tasks.tournaments_check import start_tournaments_checks
from src.utils.enums import PlayerStatus

from src.tests.fixtures import CnPlayer


@patch("src.tasks.tournaments_check.BotService")
@pytest.mark.asyncio
async def test_start_tournaments_checks(mock_bot_service, mock_db):
    # Preparation - Insert mock tournament data
    test_tournament_id = "123"
    test_tournament_id2 = "321"
    test_player_id = "456"
    test_app_id = 85
    test_day_two_tournament_id = "1011"
    player = await CnPlayer(app_id=test_app_id, enabled=True, status=PlayerStatus.IDLE.value, player_id=test_player_id).insert()
    playing_player = await Player(
        app_id=test_app_id,
        enabled=True,
        status=PlayerStatus.PLAYING.value,
        player_id="playing_player",
    ).insert()
    wont_be_processed = await FutureTournamentCheck(tournament_id="not_checked", player_id=playing_player.player_id, app_id=test_app_id).insert()
    await FutureTournamentCheck(tournament_id=test_tournament_id, player_id=player.player_id, app_id=test_app_id).insert()
    await FutureTournamentCheck(tournament_id=test_tournament_id2, player_id=player.player_id, app_id=test_app_id).insert()

    await MultiflightConfiguration(
        multi_flight_id=1,
        day_one_tournament_ids=[test_tournament_id, test_tournament_id2],
        day_two_tournament_id=test_day_two_tournament_id,
        app_id=test_app_id,
    ).insert()

    mock_bot_service.start_bot = AsyncMock()
    mock_bot_service.start_bot.return_value = BotResponse(redis_data={}, bot_id="bot1", player=player.to_response())

    config = await MultiflightConfiguration.find_one({"multi_flight_id": 1, "app_id": test_app_id})
    assert test_tournament_id in config.day_one_tournament_ids

    # Run function
    await start_tournaments_checks()

    assert mock_bot_service.start_bot.call_count == 1
    checks = await FutureTournamentCheck.find(FutureTournamentCheck.checked == False).to_list()  # noqa: E712
    assert len(checks) == 2
    assert wont_be_processed in checks

    await start_tournaments_checks()

    assert mock_bot_service.start_bot.call_count == 2
    checks = await FutureTournamentCheck.find().to_list()  # noqa: E712
    assert len(checks) == 1
    assert wont_be_processed in checks


@patch("src.tasks.tournaments_check.BotService")
@pytest.mark.asyncio
async def test_start_tournaments_checks_skips_idle_player(mock_bot_service, mock_db):

    player = await Player(
        app_id=85, enabled=True, status=PlayerStatus.PLAYING.value, player_id="p1"
    ).save()

    await FutureTournamentCheck(
        tournament_id="t", player_id=player.player_id, app_id=85
    ).save()

    # player not idle, check not called
    await start_tournaments_checks()
    checks = await FutureTournamentCheck.find().to_list()
    assert len(checks) == 1

    player.status = PlayerStatus.IDLE.value
    await player.save()

    # no mf config, check deleted
    await start_tournaments_checks()
    checks = await FutureTournamentCheck.find().to_list()
    assert len(checks) == 0

    await FutureTournamentCheck(
        tournament_id="t1", player_id=player.player_id, app_id=85
    ).save()
    await MultiflightConfiguration(
        multi_flight_id=1,
        day_one_tournament_ids=['t1'],
        day_two_tournament_id='t2',
        app_id=85,
    ).save()

    mock_bot_service.start_bot = AsyncMock()
    mock_bot_service.start_bot.return_value = BotResponse(redis_data={}, bot_id="bot1", player=player.to_response())

    await start_tournaments_checks()
    checks = await FutureTournamentCheck.find().to_list()
    assert len(checks) == 0
    mock_bot_service.start_bot.assert_awaited_once()
