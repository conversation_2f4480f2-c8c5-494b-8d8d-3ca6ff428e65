from datetime import datetime, timezone, time
from typing import List, Optional, Dict
from zoneinfo import ZoneInfo
import uuid

from beanie import Document
from pydantic import BaseModel, Field, ConfigDict
from pymongo import ASCENDING, IndexModel
from src.utils.enums import AppType, PlatformType, CurrencyType, RegistrationStatus, StrategyProfile

from src.schemas.responses import (
    GamingConfigurationResponse,
    TableResponse,
    PlayerResponse,
    PlayerStatusResponse,
    PlayerTableResponse,
    PlayerBalanceResponse,
    AutoStartActionResponse,
    AutoStartConfigResponse,
    LaunchResponse,
    PlayerShortResponse,
    MultiflightConfigurationResponse,
    FutureLaunchResponse,
    IPPoolConfigurationResponse,
    PlayTimeRangeResponse,
    RegistrationAutomationConfigResponse,
    PendingRegistrationResponse,
    TicketResponse,
)


class GamingConfiguration(BaseModel):
    game: str
    blinds: List[float]
    ante: int = 0
    game_mode: int = 0
    room_mode: int = 0
    straddle: bool

    def to_response(self) -> GamingConfigurationResponse:
        return GamingConfigurationResponse(
            game=self.game,
            blinds=self.blinds,
            ante=self.ante,
            game_mode=self.game_mode,
            room_mode=self.room_mode,
            straddle=self.straddle,
        )


class MultiflightConfiguration(Document):
    app_id: int
    multi_flight_id: int
    day_one_tournament_ids: List[str]
    day_two_tournament_id: Optional[str] = None
    date_updated: Optional[datetime] = datetime.now(ZoneInfo("UTC"))

    class Settings:
        name = "multi_flight_configurations"

    def to_response(self) -> MultiflightConfigurationResponse:
        return MultiflightConfigurationResponse(
            multi_flight_id=self.multi_flight_id,
            day_one_tournament_ids=self.day_one_tournament_ids,
            day_two_tournament_id=self.day_two_tournament_id,
            date_updated=self.date_updated,
            app_id=self.app_id,
        )


class TournamentConfiguration(Document):
    app_id: int
    tournament_id: str
    game_pool: float
    game_pool_updated_manually: bool = False
    max_reentry_count: int = 0
    scheduling_min_delay_sec: int | None = None
    scheduling_max_delay_sec: int | None = None
    date_updated: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    class Settings:
        name = "tournament_configurations"


class Tournament(Document):
    tournament_id: str
    tournament_name: str
    tournament_name_eng: Optional[str] = None
    starting_time: datetime
    late_registration_time: Optional[datetime] = None
    seats_per_table: int
    registration_fee: float
    service_fee: Optional[float] = 0
    game_pool: float = 0
    overlay: Optional[float] = 0
    currency: str
    registered_count: int = 0
    joined_count: int = 0

    status: Optional[int] = None

    mtt_mode: Optional[int] = None
    tournament_mode: Optional[int] = None
    game_mode: Optional[int] = None
    is_satellite_mode: Optional[bool] = False
    multi_flight_id: Optional[int] = None
    multi_flight_level: Optional[int] = None

    sign_up_options: Optional[str] = None

    app_id: int

    date_updated: Optional[datetime] = datetime.now(ZoneInfo("UTC"))

    class Settings:
        name = "tournaments"


class Ticket(BaseModel):
    ticket_id: int
    tool_id: int

    def to_response(self) -> TicketResponse:
        return TicketResponse(
            ticket_id=self.ticket_id,
            tool_id=self.tool_id,
        )


class PlayerBalance(BaseModel):
    diamond: Optional[float] = 0
    gold: Optional[float] = 0
    usdt: Optional[float] = 0
    usd: Optional[float] = 0
    tickets: Optional[List[Ticket]] = []

    def to_response(self) -> PlayerBalanceResponse:
        return PlayerBalanceResponse(
            diamond=self.diamond or 0,
            gold=self.gold or 0,
            usdt=self.usdt or 0,
            usd=self.usd or 0,
            tickets=[ticket.to_response() for ticket in self.tickets] if self.tickets else [],
        )


class Launch(Document):
    """
    Represents a single launch of a bot on a table for a player.
    Does not get deleted.
    """

    launch_id: str
    player_id: str
    table_id: str
    app_id: int
    config_id: int | None = None
    error: str | None = None
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime | None = None
    strategy_profile: Optional[str] = None

    finished: bool = False

    # Tables automation sets this field on bot start, controls game duration, may be exceeded
    soft_max_hands_played: int | None = 0
    can_reenter_tournament: bool | None = None

    class Settings:
        name = "launches"

    def to_response(self) -> LaunchResponse:
        return LaunchResponse(
            launch_id=self.launch_id,
            player_id=self.player_id,
            table_id=self.table_id,
            app_id=self.app_id,
            config_id=self.config_id,
            error=self.error,
            created_at=self.created_at,
            updated_at=self.updated_at,
            finished=self.finished,
        )


class FutureLaunch(Document):
    player_id: str
    tournament_id: str
    app_id: int
    starting_time: Optional[datetime] = None
    ticket_id: Optional[str] = None

    class Settings:
        name = "future_launches"

    def to_response(self) -> FutureLaunchResponse:
        return FutureLaunchResponse(
            player_id=self.player_id,
            tournament_id=self.tournament_id,
            app_id=self.app_id,
            starting_time=self.starting_time,
        )


class FutureTournamentCheck(Document):
    player_id: str
    tournament_id: str
    app_id: int
    checked: bool = False
    launch_id: Optional[str] = None

    class Settings:
        name = "future_tournament_checks"


class PlayTimeRange(BaseModel):
    start: time
    end: time


class Player(Document):
    player_id: str
    bot_type: Optional[str] = None
    app_id: int
    allowed_games: List[AppType] = []
    platform_id: Optional[PlatformType] = PlatformType.WPK.value  # TODO: make required after migration
    club_ids: List[int] | None = []
    country_code: Optional[str] = None
    enabled: bool
    status: str
    launch_id: Optional[str] = None
    bot_id: Optional[str] = None
    table_id: Optional[str] = None
    created_at: datetime | None = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime | None = None
    last_error: Optional[str] = None
    balance: PlayerBalance = PlayerBalance()
    need_balance_update: Optional[bool] = False
    ticket_id: Optional[str] = None
    play_time_range: PlayTimeRange = PlayTimeRange(start=time.min, end=time.max)
    receiver_id: Optional[int] = None
    receiver_username: Optional[str] = None
    strategy_profile: Optional[str] = None
    avatar_url: Optional[str] = None
    avatar_changed: Optional[bool] = False

    # R2 stats
    hands_played: int = 0
    total_buy_in: Optional[int] = 0
    last_buy_in: Optional[int] = 0
    rebuy_count: Optional[int] = 0
    stack: Optional[float] = 0

    # R3 stats
    chips: Optional[float] = 0
    rank: Optional[int] = 0

    model_config = ConfigDict(
        use_enum_values=True,
    )

    class Settings:
        name = "players"
        validate_on_save = True
        indexes = [
            IndexModel([("player_id", ASCENDING), ("app_id", ASCENDING)], unique=True),
        ]
        bson_encoders = {
            time: lambda v: v.strftime("%H:%M"),  # how to serialize datetime.time
        }

    def to_response(self) -> PlayerResponse:
        return PlayerResponse(
            player_id=self.player_id,
            app_id=self.app_id,
            allowed_games=self.allowed_games,
            enabled=self.enabled,
            country_code=self.country_code or "",
            status=self.status,
            bot_type=self.bot_type,
            bot_id=self.bot_id,
            table_id=self.table_id,
            last_error=self.last_error,
            created_at=self.created_at,
            updated_at=self.updated_at,
            hands_played=self.hands_played,
            total_buy_in=self.total_buy_in,
            last_buy_in=self.last_buy_in,
            rebuy_count=self.rebuy_count,
            stack=self.stack,
            chips=self.chips,
            rank=self.rank,
            balance=self.balance.to_response(),
            need_balance_update=self.need_balance_update,
            ticket_id=self.ticket_id,
            launch_id=self.launch_id,
            play_time_range=PlayTimeRangeResponse(**self.play_time_range.model_dump()),
            platform_id=self.platform_id or PlatformType.WPK.value,  # default to WPK if not set
            strategy_profile=self.strategy_profile,
            avatar_changed=self.avatar_changed,
            avatar_url=self.avatar_url,
        )

    def to_status_response(self) -> PlayerStatusResponse:
        return PlayerStatusResponse(
            player_id=self.player_id,
            status=self.status,
        )

    def to_table_response(self) -> PlayerTableResponse:
        return PlayerTableResponse(
            player_id=self.player_id,
            bot_id=self.bot_id,
            status=self.status,
        )

    def to_short_response(self) -> PlayerShortResponse:
        return PlayerShortResponse(
            player_id=self.player_id,
            status=self.status,
            rank=self.rank,
            chips=self.chips,
        )

    def is_in_play_time_range(self, play_time: time):
        # we support playtime rnges that can be overnight, ie 21:00 - 04:00
        if self.play_time_range.start < self.play_time_range.end:
            return self.play_time_range.start < play_time < self.play_time_range.end
        else:
            return self.play_time_range.start < play_time or play_time < self.play_time_range.end


class TaskLaunch(Document):
    player_id: str
    type: str

    # params for transfer
    transfer_amount: int | None = 0
    receiver_id: int | None = 0
    receiver_username: str | None = None
    currency: CurrencyType | None = None

    model_config = ConfigDict(
        use_enum_values=True,
    )

    class Settings:
        name = "task_launches"


class Table(Document):
    table_id: str
    table_name: str
    app_id: int
    club_id: int | None = None
    gaming_configuration: (
        GamingConfiguration  # Beanie Link is not used here because of incompatible DocumentDB
    )
    currency: CurrencyType
    players_total: Optional[int] = 0
    empty_seats: int
    date_updated: Optional[datetime] = datetime.now(ZoneInfo("UTC"))

    class Settings:
        name = "tables"

    def to_response(self, bots: List[Player]) -> TableResponse:
        return TableResponse(
            table_id=self.table_id,
            table_name=self.table_name,
            app_id=self.app_id,
            gaming_configuration=self.gaming_configuration.to_response(),
            currency=self.currency,
            empty_seats=self.empty_seats,
            players_total=self.players_total,
            bots=[player.to_table_response() for player in bots],
        )


class StatusChange(BaseModel):
    player_id: str
    old_status: str
    new_status: str
    date_updated: datetime

    class Settings:
        name = "status_changes"


class AutoStartConfig(Document):
    """Tournament automation config"""

    app_id: int
    enabled: bool = False
    cover_before_start: float = 0.1
    cover_late_registration: float = 0.8
    bot_min_delay_sec: int = 30
    bot_max_delay_sec: int = 90
    schedule_min_players: int = 1
    check_interval_sec: int = 300
    check_before_start_min: int = 60
    min_prize_pool: int = 0
    reentry_probability: float = 0.35

    class Settings:
        name = "auto_start_configs"

    def to_response(self) -> AutoStartConfigResponse:
        return AutoStartConfigResponse(
            app_id=self.app_id,
            enabled=self.enabled,
            cover_before_start=self.cover_before_start,
            cover_late_registration=self.cover_late_registration,
            bot_min_delay_sec=self.bot_min_delay_sec,
            bot_max_delay_sec=self.bot_max_delay_sec,
            schedule_min_players=self.schedule_min_players,
            check_interval_sec=self.check_interval_sec,
            check_before_start_min=self.check_before_start_min,
            min_prize_pool=self.min_prize_pool,
            reentry_probability=self.reentry_probability,
        )


class AutoStartAction(Document):
    """History of tourmanet automation bot start events"""

    app_id: int
    tournament_id: str
    number_of_players: int
    started: datetime
    finished: datetime

    class Settings:
        name = "auto_start_actions"

    def to_response(self) -> AutoStartActionResponse:
        return AutoStartActionResponse(
            app_id=self.app_id,
            tournament_id=self.tournament_id,
            number_of_players=self.number_of_players,
            started=self.started,
            finished=self.finished,
        )


class IPPoolConfiguration(Document):
    ip_conf_id: str
    full_proxy_url: str
    external_ip: str
    country_code: Optional[str] = None
    host: Optional[str] = None
    port: Optional[int] = None
    username: Optional[str] = None
    password: Optional[str] = None
    assigned_player_ids: Optional[List[str]] = []

    def to_response(self) -> IPPoolConfigurationResponse:
        return IPPoolConfigurationResponse(
            ip_conf_id=self.ip_conf_id,
            full_proxy_url=self.full_proxy_url,
            external_ip=self.external_ip,
            country_code=self.country_code if self.country_code is not None else None,
            host=self.host,
            port=self.port,
            username=self.username,
            password=self.password,
            assigned_player_ids=self.assigned_player_ids,
        )

    class Settings:
        name = "ip_pool_configuration"


class TablesAutomationConfig(Document):
    config_id: int
    app_id: int
    club_id: int | None = None
    is_enabled: bool = False
    start_interval: int
    start_last_update: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    stop_interval: int
    stop_last_update: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    big_blinds: List[int]
    table_rebuy_threshold: int = 0
    room_modes: List[int]
    players_count: List[int] = [0, 7]  # min and max players on the table to start bot
    min_players_count_to_stop: int = 0  # if number of players on table <= this value, bot will stop
    max_players_count_to_stop: int = 9  # if number of players on table > this value, bot will stop
    ante: int
    currencies: List[CurrencyType]
    max_bots_per_table: Optional[int] = 1
    max_rebuy_count: Optional[int] = 5
    buy_in_multipliers: Optional[List[int]] = []
    # how many seats must be occupied on all other tables to start a bot - so that bots don't start on empty tables until other tables are filled
    occupancy_threshold: Optional[int] = 0
    strategy_profile: Dict[StrategyProfile, int] = {}  # Strategy profiles with their weights in percentage
    tables_affected_percentage: int = 100  # Config will start bots only if table_id % 100 < tables_affected_percentage

    zoom_allowed_bots_percent: int = 0  # Percentage of bots allowed on zoom tables
    zoom_start_bots_per_interval: int = 1  # How many bots to start per interval on zoom tables, for others we are limited to one bot per table

    # auto-withdraw/buyout params
    withdraw_amount: int = 0
    withdraw_threshold: int = 0

    class Settings:
        name = "tables_automation_config"

    @staticmethod
    async def get_next_config_id() -> int:
        """Get the next config_id across all records."""
        max_config = await TablesAutomationConfig.find().sort("-config_id").first_or_none()

        return (max_config.config_id + 1) if max_config else 1


class RegistrationAutomationConfig(Document):
    """Configuration for registration automation. Should exist in a single instance."""

    config_id: int
    is_enabled: bool = False

    min_delay_sec: int = 30
    max_delay_sec: int = 90

    last_registration_time: datetime | None = None
    next_registration_time: datetime | None = None

    class Settings:
        name = "registration_automation_configs"

    def to_response(self) -> RegistrationAutomationConfigResponse:
        return RegistrationAutomationConfigResponse(**self.model_dump())


class PendingRegistration(Document):
    """Pending registration for a player, to be processed later."""

    pending_registration_id: str = Field(default_factory=lambda: f"pr-{uuid.uuid4()}")
    username: Optional[str] = None
    account: Optional[str] = None
    password: Optional[str] = None
    country_code: str
    area_code: Optional[str] = None
    phone_number: Optional[str] = None
    receiver_id: Optional[int] = None
    receiver_username: Optional[str] = None
    email: Optional[str] = None
    app_id: int
    platform_id: int
    extra_data: Optional[dict] = None
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    error: Optional[str] = None
    status: str = RegistrationStatus.PENDING.value

    player_id: Optional[str] = None  # Player ID if registration is successful
    user_id: Optional[str] = None  # User ID if registration is successful

    class Settings:
        name = "pending_registrations"

    def to_response(self) -> PendingRegistrationResponse:
        response_dict = self.model_dump(exclude={"extra_data", "id"}) | self.extra_data or {}
        return PendingRegistrationResponse(**response_dict)
