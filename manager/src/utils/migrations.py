from datetime import datetime, timezone, timedelta

from beanie import BulkWriter
from beanie.operators import Or

from src.utils.enums import PlatformType, AppType
from src.db.models import RegistrationAutomationConfig, Launch, Player
from src.utils.logging import logger


async def add_platform_to_players():
    name = "tasks.add_platform_to_players"
    logger.debug(name, "Adding platform_id to players...")

    async with BulkWriter() as bulk_writer:
        players = await Player.find(
            Or(
                Player.app_id == AppType.R4.value,
                Player.platform_id == None,  # noqa: E711
            )
        ).to_list()
        for player in players:
            if player.app_id == AppType.R4.value:
                # R4 is the only app that uses RWPK platform
                player.platform_id = PlatformType.RWPK.value

            elif player.platform_id is None:
                # Default to WPK platform if not set
                player.platform_id = PlatformType.WPK.value
                await player.save()

        logger.debug(name, f"Updated {len(players)} players with platform_id")
        await bulk_writer.commit()


async def create_registration_automation_config():
    name = "tasks.create_registration_automation_config"
    logger.debug(name, "Checking if registration configuration exists...")
    config = await RegistrationAutomationConfig.find().first_or_none()
    if not config:
        logger.info(name, "Registration configuration not found. Creating default configuration...")
        config = RegistrationAutomationConfig(config_id=1, is_enabled=False, min_delay_sec=30, max_delay_sec=60)
        await config.insert()


async def delete_old_launches():
    name = "tasks.delete_old_launches"

    threshold_date = datetime.now(timezone.utc) - timedelta(days=30)
    old_launches = await Launch.find(Launch.created_at < threshold_date).count()

    logger.debug(name, f"Deleting {old_launches} old launches")

    await Launch.find(Launch.created_at < threshold_date).delete()


async def set_player_allowed_games_from_app_id():
    name = "tasks.set_player_allowed_games_from_app_id"
    logger.debug(name, "Setting allowed games for players based on app_id...")

    async with BulkWriter() as bulk_writer:
        players = await Player.find(
            Player.allowed_games == [],
        ).to_list()
        for player in players:
            if player.app_id and not player.allowed_games:
                player.allowed_games = [AppType(player.app_id)]
                await player.save()

        logger.debug(name, f"Updated {len(players)} players with allowed games from app_id")
        await bulk_writer.commit()
